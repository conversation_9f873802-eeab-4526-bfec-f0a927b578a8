{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\ThuneeAPI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "projectName": "ThuneeAPI.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "projectName": "ThuneeAPI.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj", "projectName": "ThuneeAPI.Infrastructure", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\ThuneeAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\ThuneeAPI.csproj", "projectName": "ThuneeAPI", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\ThuneeAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj"}, "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}