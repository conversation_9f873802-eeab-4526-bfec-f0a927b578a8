2025-07-24 13:33:03.394 +02:00 [INF] Starting Thunee API Server
2025-07-24 13:35:03.251 +02:00 [INF] Login attempt for username: <PERSON>risan123
2025-07-24 13:35:05.416 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-07-24 13:37:45.550 +02:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

ThuneeAPI.Controllers.CompetitionPhaseSimpleController.AdvancePhase (ThuneeAPI)
ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase (ThuneeAPI)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-24 13:39:45.634 +02:00 [INF] Starting Thunee API Server
