# Competition Phase Management - Database Setup Instructions

## Overview
This document provides step-by-step instructions to set up the database stored procedures required for the Competition Phase Management system.

## Prerequisites
- SQL Server Management Studio (SSMS) or similar SQL client
- Access to the `GoldRushThunee` database
- Database connection: `Server=192.168.20.121; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;`

## Database Schema Status
✅ **Already Complete**: The following tables and columns have been successfully added:
- `Competitions` table with Phase management columns (`Phase`, `PhaseEndDate`, `MaxGamesPerPhase`)
- `CompetitionTeams` table with Phase management columns (`Phase`, `IsEliminated`, `AdvancedToNextPhase`, `PhaseEliminatedAt`)
- `CompetitionTeamInvites` table (existing)
- `CompetitionTeamPhaseStats` table (existing)
- `CompetitionPhaseLobbies` table (existing)
- `CompetitionPhaseLobbyTeams` table (existing)

## Step 1: Run the Stored Procedures Script

1. **Open SQL Server Management Studio (SSMS)**
2. **Connect to your database server** using the connection details above
3. **Open the stored procedures script**: `ThuneeAPI/database-stored-procedures.sql`
4. **Execute the entire script** by pressing F5 or clicking "Execute"

The script will create the following stored procedures:

### Core Phase Management Procedures:
- `SP_AdvanceCompetitionPhase` - Advances competition to new phase and manages team transitions
- `SP_GetEligibleTeamsForPhase` - Gets teams eligible for advancement to next phase
- `SP_GetTeamsByPhase` - Gets all teams in a specific phase
- `SP_CheckTeamEligibilityForGames` - Checks if a team is eligible to play games

### Knockout Phase Management Procedures:
- `SP_CreateCompetitionPhaseLobby` - Creates a lobby for knockout phase matches
- `SP_SetPhaseLobbyWinner` - Sets the winner of a knockout phase lobby

## Step 2: Verify Installation

After running the script, verify that all stored procedures were created successfully:

```sql
-- Check if all stored procedures exist
SELECT name, create_date, modify_date 
FROM sys.objects 
WHERE type = 'P' 
  AND name IN (
    'SP_AdvanceCompetitionPhase',
    'SP_GetEligibleTeamsForPhase', 
    'SP_GetTeamsByPhase',
    'SP_CheckTeamEligibilityForGames',
    'SP_CreateCompetitionPhaseLobby',
    'SP_SetPhaseLobbyWinner'
  )
ORDER BY name;
```

Expected result: 6 stored procedures should be listed.

## Step 3: Test the System

### Test Competition Phase Advancement

1. **Test the advance endpoint**:
   ```
   POST https://localhost:57229/api/competitions/4860c19d-e3f3-4e2d-b359-275527461bd6/phases/advance
   Content-Type: application/json
   
   {
     "newPhase": "Top16"
   }
   ```

2. **Check eligible teams**:
   ```
   GET https://localhost:57229/api/competitions/4860c19d-e3f3-4e2d-b359-275527461bd6/phases/eligible-teams
   ```

3. **View teams in specific phase**:
   ```
   GET https://localhost:57229/api/competitions/4860c19d-e3f3-4e2d-b359-275527461bd6/phases/teams?phase=Top32
   ```

## How the Phase System Works

### Phase Progression:
1. **Leaderboard** → **Top32** (Top 32 teams advance)
2. **Top32** → **Top16** (Top 16 teams advance) 
3. **Top16** → **Top8** (Top 8 teams advance)
4. **Top8** → **Top4** (Top 4 teams advance)
5. **Top4** → **Final** (Top 2 teams advance)
6. **Final** → **Completed** (1 winner)

### Team Management:
- **Advancing Teams**: Teams that qualify move to the next phase
- **Eliminated Teams**: Teams that don't qualify are marked as `IsEliminated = 1`
- **Game Eligibility**: Only non-eliminated teams can play games
- **Phase Tracking**: Each team's current phase is tracked in `CompetitionTeams.Phase`

### Game Limits:
- **Top32 Phase**: Max 10 games per team (configurable)
- **Knockout Phases**: Single elimination games

## API Endpoints Available

### Competition Phase Management:
- `POST /api/competitions/{id}/phases/advance` - Advance competition to next phase
- `GET /api/competitions/{id}/phases/teams?phase={phase}` - Get teams in specific phase
- `GET /api/competitions/{id}/phases/eligible-teams` - Get teams eligible for next phase

### Expected Behavior After Setup:

1. **Phase Advancement**: When advancing from Leaderboard to Top32:
   - Top 32 teams (by total points) advance to Top32 phase
   - Remaining teams are eliminated and cannot play games
   - Competition phase is updated with end date and game limits

2. **Team Eligibility**: Eliminated teams:
   - Cannot join new games
   - Can only view leaderboards
   - Are marked with `IsEliminated = 1` and `PhaseEliminatedAt` timestamp

3. **Matchmaking**: Only teams in the current competition phase can be matched together

## Troubleshooting

### If stored procedures fail to create:
1. Check database permissions for the `EG-Dev` user
2. Ensure the database name is correct (`GoldRushThunee`)
3. Verify all required tables exist

### If API endpoints return errors:
1. Check server logs for detailed error messages
2. Verify database connection in appsettings.json
3. Ensure all stored procedures were created successfully

### If team advancement doesn't work:
1. Check that teams have `IsComplete = 1` (both players joined)
2. Verify teams are not already eliminated
3. Check competition status is 'active'

## Support
If you encounter any issues during setup, please check:
1. Database connection string
2. User permissions
3. Table schema matches expected structure
4. All stored procedures created successfully

The system is now ready for Competition Phase Management! 🎉
