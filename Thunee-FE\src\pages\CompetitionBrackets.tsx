"use client";
import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Trophy, Users, Clock, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import BurgerMenu from "@/components/BurgerMenu";
import CompetitionBracket from "@/components/CompetitionBracket";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  description: string;
  phase: string;
  phaseEndDate?: string;
  status: string;
  startDate: string;
  endDate: string;
}

export default function CompetitionBrackets() {
  const { competitionId } = useParams<{ competitionId: string }>();
  const navigate = useNavigate();
  
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [brackets, setBrackets] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    if (competitionId) {
      loadCompetitionData();
      
      // Auto-refresh every 30 seconds
      const interval = setInterval(() => {
        loadBrackets();
      }, 30000);
      
      return () => clearInterval(interval);
    }
  }, [competitionId]);

  const loadCompetitionData = async () => {
    try {
      setIsLoading(true);
      
      // Load competition details
      const competitionData = await apiService.getCompetitionById(competitionId!);
      setCompetition(competitionData);
      
      // Load brackets if in knockout phase
      if (isKnockoutPhase(competitionData.phase)) {
        await loadBrackets();
      }
      
    } catch (error) {
      console.error("Error loading competition data:", error);
      toast.error("Failed to load competition data");
    } finally {
      setIsLoading(false);
    }
  };

  const loadBrackets = async () => {
    try {
      const brackets = await apiService.getPhaseBrackets(competitionId!);
      setBrackets(brackets);
      setLastUpdated(new Date());
    } catch (error) {
      console.error("Error loading brackets:", error);
    }
  };

  const handleRefresh = async () => {
    await loadBrackets();
    toast.success("Brackets refreshed");
  };

  const isKnockoutPhase = (phase: string): boolean => {
    return ["Top16", "Top8", "Top4", "Final"].includes(phase);
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Leaderboard": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Top32": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top16": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Top8": return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Top4": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Final": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark text-white flex flex-col relative">
        <BurgerMenu />
        <div className="flex items-center justify-center flex-1">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
            <p className="text-gray-400">Loading tournament brackets...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark text-white flex flex-col relative">
      <BurgerMenu />
      
      <div className="flex-1 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate("/competitions")}
                className="text-[#E1C760] hover:bg-[#E1C760]/10"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="flex items-center gap-2">
                <Trophy className="h-6 w-6 text-[#E1C760]" />
                <div>
                  <h1 className="text-2xl font-bold text-[#E1C760]">Tournament Brackets</h1>
                  {competition && (
                    <p className="text-gray-400">{competition.name}</p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                className="border-[#E1C760] bg-[#E1C760]/10 text-[#E1C760] hover:bg-[#E1C760]/20"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              
              {competition && (
                <Badge className={getPhaseColor(competition.phase)}>
                  {competition.phase} Phase
                </Badge>
              )}
            </div>
          </div>

          {competition && (
            <div className="space-y-6">
              {/* Competition Info */}
              <Card className="bg-black/50 border-[#E1C760]/30">
                <CardHeader>
                  <CardTitle className="text-[#E1C760]">{competition.name}</CardTitle>
                  <CardDescription className="text-gray-400">
                    {competition.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Competition Period</p>
                      <p className="text-white font-medium">
                        {formatDate(competition.startDate)} - {formatDate(competition.endDate)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Current Phase</p>
                      <Badge className={getPhaseColor(competition.phase)}>
                        {competition.phase}
                      </Badge>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-400">Last Updated</p>
                      <p className="text-white font-medium">
                        {formatTime(lastUpdated)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Brackets */}
              {isKnockoutPhase(competition.phase) ? (
                <Card className="bg-black/50 border-[#E1C760]/30">
                  <CardHeader>
                    <CardTitle className="text-[#E1C760] flex items-center gap-2">
                      <Trophy className="h-5 w-5" />
                      Knockout Tournament Brackets
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      Follow the tournament progression through each knockout phase
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <CompetitionBracket 
                      competitionId={competitionId!}
                      brackets={brackets}
                      isLoading={false}
                    />
                  </CardContent>
                </Card>
              ) : (
                <Card className="bg-black/50 border-[#E1C760]/30">
                  <CardContent className="text-center py-12">
                    <Users className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">
                      {competition.phase === "Leaderboard" ? "Leaderboard Phase" : "Qualification Phase"}
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {competition.phase === "Leaderboard" 
                        ? "Teams are currently competing in the leaderboard phase. Knockout brackets will be available when the tournament reaches the Top 16 phase."
                        : "Teams are competing in the qualification rounds. Brackets will be available for knockout phases."
                      }
                    </p>
                    <Button
                      onClick={() => navigate(`/competitions/${competitionId}/leaderboard`)}
                      className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                    >
                      <Trophy className="h-4 w-4 mr-2" />
                      View Leaderboard
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* Phase Information */}
              {competition.phaseEndDate && (
                <Card className="bg-black/50 border-[#E1C760]/30">
                  <CardContent className="py-4">
                    <div className="flex items-center justify-center gap-4 text-sm">
                      <Clock className="h-4 w-4 text-[#E1C760]" />
                      <span className="text-gray-400">
                        {competition.phase} phase ends on {formatDate(competition.phaseEndDate)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
