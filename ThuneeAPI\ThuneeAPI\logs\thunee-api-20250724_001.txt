2025-07-24 09:31:04.821 +02:00 [INF] Starting Thunee API Server
2025-07-24 09:35:12.737 +02:00 [INF] Starting Thunee API Server
2025-07-24 09:37:49.220 +02:00 [INF] Login attempt for username: Sherisan123
2025-07-24 09:37:54.197 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-07-24 09:44:25.082 +02:00 [ERR] Error getting competitions
Microsoft.Data.SqlClient.SqlException (0x80131904): Connection Timeout Expired.  The timeout period elapsed during the post-login phase.  The connection could have timed out while waiting for server to complete the login process and respond; Or it could have timed out while attempting to create multiple active connections.  The duration spent while attempting to connect to this server was - [Pre-Login] initialization=1943; handshake=12044; [Login] initialization=0; authentication=0; [Post-Login] complete=1012; 
 ---> System.ComponentModel.Win32Exception (258): The wait operation timed out.
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ThrowExceptionAndWarning(Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ReadSniError(TdsParserStateObject stateObj, UInt32 error)
   at Microsoft.Data.SqlClient.TdsParserStateObject.ReadSniSyncOverAsync()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryReadNetworkPacket()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryPrepareBuffer()
   at Microsoft.Data.SqlClient.TdsParserStateObject.TryReadByte(Byte& value)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.CompleteLogin(Boolean enlistOK)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.AttemptOneLogin(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, TimeoutTimer timeout, Boolean withFailover)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.LoginNoFailover(ServerInfo serverInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString connectionOptions, SqlCredential credential, TimeoutTimer timeout)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds.OpenLoginEnlist(TimeoutTimer timeout, SqlConnectionString connectionOptions, SqlCredential credential, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance)
   at Microsoft.Data.SqlClient.SqlInternalConnectionTds..ctor(DbConnectionPoolIdentity identity, SqlConnectionString connectionOptions, SqlCredential credential, Object providerInfo, String newPassword, SecureString newSecurePassword, Boolean redirectedUserInstance, SqlConnectionString userConnectionOptions, SessionData reconnectSessionData, Boolean applyTransientFaultHandling, String accessToken, DbConnectionPool pool, Func`3 accessTokenCallback)
   at Microsoft.Data.SqlClient.SqlConnectionFactory.CreateConnection(DbConnectionOptions options, DbConnectionPoolKey poolKey, Object poolGroupProviderInfo, DbConnectionPool pool, DbConnection owningConnection, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.CreatePooledConnection(DbConnectionPool pool, DbConnection owningObject, DbConnectionOptions options, DbConnectionPoolKey poolKey, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionPool.CreateObject(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.UserCreateRequest(DbConnection owningObject, DbConnectionOptions userOptions, DbConnectionInternal oldConnection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.WaitForPendingOpen()
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 433
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 84
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 51
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionService.cs:line 23
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionsController.cs:line 33
ClientConnectionId:e2f002c2-081d-4a4a-88d8-f13e0ab61a98
Error Number:-2,State:0,Class:11
2025-07-24 09:44:25.153 +02:00 [ERR] Error getting competitions
Microsoft.Data.SqlClient.SqlException (0x80131904): Connection Timeout Expired.  The timeout period elapsed during the post-login phase.  The connection could have timed out while waiting for server to complete the login process and respond; Or it could have timed out while attempting to create multiple active connections.  The duration spent while attempting to connect to this server was - [Pre-Login] initialization=1943; handshake=12044; [Login] initialization=0; authentication=0; [Post-Login] complete=1012; 
 ---> System.ComponentModel.Win32Exception (258): The wait operation timed out.
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.InternalOpenAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 433
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 84
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 51
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionService.cs:line 23
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionsController.cs:line 33
ClientConnectionId:e2f002c2-081d-4a4a-88d8-f13e0ab61a98
Error Number:-2,State:0,Class:11
2025-07-24 09:44:25.213 +02:00 [ERR] Error getting competitions
Microsoft.Data.SqlClient.SqlException (0x80131904): Connection Timeout Expired.  The timeout period elapsed during the post-login phase.  The connection could have timed out while waiting for server to complete the login process and respond; Or it could have timed out while attempting to create multiple active connections.  The duration spent while attempting to connect to this server was - [Pre-Login] initialization=1943; handshake=12044; [Login] initialization=0; authentication=0; [Post-Login] complete=1012; 
 ---> System.ComponentModel.Win32Exception (258): The wait operation timed out.
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.InternalOpenAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 433
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 84
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 51
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionService.cs:line 23
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionsController.cs:line 33
ClientConnectionId:e2f002c2-081d-4a4a-88d8-f13e0ab61a98
Error Number:-2,State:0,Class:11
2025-07-24 09:44:25.282 +02:00 [ERR] Error getting competitions
Microsoft.Data.SqlClient.SqlException (0x80131904): Connection Timeout Expired.  The timeout period elapsed during the post-login phase.  The connection could have timed out while waiting for server to complete the login process and respond; Or it could have timed out while attempting to create multiple active connections.  The duration spent while attempting to connect to this server was - [Pre-Login] initialization=1943; handshake=12044; [Login] initialization=0; authentication=0; [Post-Login] complete=1012; 
 ---> System.ComponentModel.Win32Exception (258): The wait operation timed out.
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, UInt32 waitForMultipleObjectsTimeout, Boolean allowCreate, Boolean onlyOneCheckConnection, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionPool.TryGetConnection(DbConnection owningObject, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionFactory.TryGetConnection(DbConnection owningConnection, TaskCompletionSource`1 retry, DbConnectionOptions userOptions, DbConnectionInternal oldConnection, DbConnectionInternal& connection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.InternalOpenAsync(CancellationToken cancellationToken)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 433
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteQueryAsync[T](String sql, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 84
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.GetAllAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 51
   at ThuneeAPI.Infrastructure.Services.CompetitionService.GetCompetitionsAsync() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionService.cs:line 23
   at ThuneeAPI.Controllers.CompetitionsController.GetCompetitions() in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionsController.cs:line 33
ClientConnectionId:e2f002c2-081d-4a4a-88d8-f13e0ab61a98
Error Number:-2,State:0,Class:11
