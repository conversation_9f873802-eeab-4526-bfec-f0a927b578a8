using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using System.Security.Claims;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/competitions/{competitionId}/phases")]
public class CompetitionPhaseController : ControllerBase
{
    private readonly ICompetitionPhaseService _phaseService;
    private readonly ILogger<CompetitionPhaseController> _logger;

    public CompetitionPhaseController(
        ICompetitionPhaseService phaseService,
        ILogger<CompetitionPhaseController> logger)
    {
        _phaseService = phaseService;
        _logger = logger;
    }

    /// <summary>
    /// Advance competition to next phase (Admin only)
    /// </summary>
    [HttpPost("advance")]
    public async Task<ActionResult<CompetitionDto>> AdvancePhase(Guid competitionId, [FromBody] AdvancePhaseDto dto)
    {
        try
        {
            _logger.LogInformation("Attempting to advance competition {CompetitionId} to phase {Phase}", competitionId, dto.NewPhase);
            
            var competition = await _phaseService.AdvanceCompetitionPhaseAsync(competitionId, dto.NewPhase);
            
            _logger.LogInformation("Competition {CompetitionId} successfully advanced to phase {Phase}", competitionId, dto.NewPhase);
            
            return Ok(new
            {
                success = true,
                data = competition,
                message = $"Competition advanced to {dto.NewPhase} phase"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}. Error: {ErrorMessage}", 
                competitionId, dto.NewPhase, ex.Message);
            
            return StatusCode(500, new 
            { 
                message = "Failed to advance phase", 
                error = ex.Message,
                details = ex.InnerException?.Message
            });
        }
    }

    /// <summary>
    /// Get teams eligible for next phase
    /// </summary>
    [HttpGet("eligible-teams")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetEligibleTeams(Guid competitionId)
    {
        try
        {
            var teams = await _phaseService.GetEligibleTeamsForNextPhaseAsync(competitionId);
            return Ok(new { success = true, data = teams });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting eligible teams for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to get eligible teams", error = ex.Message });
        }
    }

    /// <summary>
    /// Get teams for current phase
    /// </summary>
    [HttpGet("teams")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetTeamsForPhase(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var teams = await _phaseService.GetTeamsForPhaseAsync(competitionId, phase);
            return Ok(new { success = true, data = teams });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to get teams for phase", error = ex.Message });
        }
    }

    /// <summary>
    /// Create a knockout phase lobby
    /// </summary>
    [HttpPost("lobbies")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> CreatePhaseLobby(Guid competitionId, [FromBody] CreateCompetitionPhaseLobbyDto dto)
    {
        try
        {
            var adminId = Guid.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? Guid.Empty.ToString());
            dto.CompetitionId = competitionId;
            
            var lobby = await _phaseService.CreatePhaseLobbyAsync(dto, adminId);
            return Ok(new { success = true, data = lobby });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating phase lobby for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to create phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Get phase lobbies
    /// </summary>
    [HttpGet("lobbies")]
    public async Task<ActionResult<List<CompetitionPhaseLobbyDto>>> GetPhaseLobbies(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var lobbies = await _phaseService.GetPhaseLobbiesAsync(competitionId, phase);
            return Ok(new { success = true, data = lobbies });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobbies for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to get phase lobbies", error = ex.Message });
        }
    }

    /// <summary>
    /// Get phase lobby by code
    /// </summary>
    [HttpGet("lobbies/{lobbyCode}")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> GetPhaseLobbyByCode(string lobbyCode)
    {
        try
        {
            var lobby = await _phaseService.GetPhaseLobbyByCodeAsync(lobbyCode);
            if (lobby == null)
                return NotFound(new { message = "Lobby not found" });
                
            return Ok(new { success = true, data = lobby });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobby by code {LobbyCode}", lobbyCode);
            return StatusCode(500, new { message = "Failed to get phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete a phase lobby
    /// </summary>
    [HttpDelete("lobbies/{lobbyId}")]
    public async Task<ActionResult> DeletePhaseLobby(Guid lobbyId)
    {
        try
        {
            await _phaseService.DeletePhaseLobbyAsync(lobbyId);
            return Ok(new { success = true, message = "Lobby deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting phase lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to delete phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Set lobby winner
    /// </summary>
    [HttpPost("lobbies/{lobbyId}/winner")]
    public async Task<ActionResult> SetLobbyWinner(Guid lobbyId, [FromBody] SetLobbyWinnerDto dto)
    {
        try
        {
            await _phaseService.SetLobbyWinnerAsync(lobbyId, dto.WinnerTeamId);
            return Ok(new { success = true, message = "Lobby winner set successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting lobby winner for lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to set lobby winner", error = ex.Message });
        }
    }

    /// <summary>
    /// Generate bracket for phase
    /// </summary>
    [HttpPost("bracket")]
    public async Task<ActionResult<BracketDto>> GenerateBracket(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var bracket = await _phaseService.GenerateBracketAsync(competitionId, phase);
            return Ok(new { success = true, data = bracket });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating bracket for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to generate bracket", error = ex.Message });
        }
    }

    /// <summary>
    /// Get competition brackets
    /// </summary>
    [HttpGet("brackets")]
    public async Task<ActionResult<List<BracketDto>>> GetCompetitionBrackets(Guid competitionId)
    {
        try
        {
            var brackets = await _phaseService.GetCompetitionBracketsAsync(competitionId);
            return Ok(new { success = true, data = brackets });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting brackets for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to get brackets", error = ex.Message });
        }
    }

    /// <summary>
    /// Process phase end
    /// </summary>
    [HttpPost("process-end")]
    public async Task<ActionResult> ProcessPhaseEnd(Guid competitionId)
    {
        try
        {
            await _phaseService.ProcessPhaseEndAsync(competitionId);
            return Ok(new { success = true, message = "Phase end processed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing phase end for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to process phase end", error = ex.Message });
        }
    }

    /// <summary>
    /// Check if competition can advance to next phase
    /// </summary>
    [HttpGet("can-advance")]
    public async Task<ActionResult<bool>> CanAdvanceToNextPhase(Guid competitionId)
    {
        try
        {
            var canAdvance = await _phaseService.CanAdvanceToNextPhaseAsync(competitionId);
            return Ok(new { success = true, data = canAdvance });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if competition {CompetitionId} can advance", competitionId);
            return StatusCode(500, new { message = "Failed to check advancement eligibility", error = ex.Message });
        }
    }

    /// <summary>
    /// Calculate phase rankings
    /// </summary>
    [HttpGet("rankings")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> CalculatePhaseRankings(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var rankings = await _phaseService.CalculatePhaseRankingsAsync(competitionId, phase);
            return Ok(new { success = true, data = rankings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating rankings for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to calculate rankings", error = ex.Message });
        }
    }
}

public class AdvancePhaseDto
{
    public string NewPhase { get; set; } = string.Empty;
}

public class SetLobbyWinnerDto
{
    public Guid WinnerTeamId { get; set; }
}
