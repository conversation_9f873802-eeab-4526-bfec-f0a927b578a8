2025-07-29 10:06:15.327 +02:00 [INF] Starting Thunee API Server
2025-07-29 10:06:17.615 +02:00 [INF] Login attempt for username: <PERSON>risan123
2025-07-29 10:06:18.702 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-07-29 10:06:19.338 +02:00 [INF] Game settings retrieved successfully
2025-07-29 10:06:19.671 +02:00 [INF] Game settings retrieved successfully
2025-07-29 10:09:03.730 +02:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Unable to resolve service for type 'ThuneeAPI.Application.Interfaces.ICompetitionPhaseService' while attempting to activate 'ThuneeAPI.Controllers.CompetitionPhaseController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
   at lambda_method82(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-29 10:11:41.167 +02:00 [INF] Starting Thunee API Server
2025-07-29 10:11:45.414 +02:00 [INF] Attempting to advance competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-07-29 10:11:45.430 +02:00 [INF] Advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-07-29 10:11:46.443 +02:00 [INF] Competition "4860c19d-e3f3-4e2d-b359-275527461bd6" successfully advanced to phase Top32
