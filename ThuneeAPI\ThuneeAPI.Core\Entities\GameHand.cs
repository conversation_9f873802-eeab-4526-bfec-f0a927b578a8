using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class GameHand
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid GameId { get; set; }
    
    public int BallNumber { get; set; }
    public int HandNumber { get; set; }
    
    public Guid WinnerPlayerId { get; set; }
    public int Points { get; set; } = 0;
    
    [MaxLength(10)]
    public string? TrumpSuit { get; set; } // hearts, diamonds, clubs, spades
    
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Game Game { get; set; } = null!;
    public virtual User WinnerPlayer { get; set; } = null!;
    public virtual ICollection<PlayedCard> PlayedCards { get; set; } = new List<PlayedCard>();
}

public class GameBall
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid GameId { get; set; }
    
    public int BallNumber { get; set; }
    
    public int Team1Score { get; set; } = 0;
    public int Team2Score { get; set; } = 0;
    
    public int? WinnerTeam { get; set; } // 1 or 2
    
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Game Game { get; set; } = null!;
}

public class PlayedCard
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid GameHandId { get; set; }
    public Guid PlayerId { get; set; }
    
    [Required]
    [MaxLength(10)]
    public string CardSuit { get; set; } = string.Empty; // hearts, diamonds, clubs, spades
    
    [Required]
    [MaxLength(2)]
    public string CardValue { get; set; } = string.Empty; // 9, 10, J, Q, K, A
    
    public int PlayOrder { get; set; } // 1-4
    
    public DateTime PlayedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual GameHand GameHand { get; set; } = null!;
    public virtual User Player { get; set; } = null!;
}

public class CompetitionTeam
{
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid CompetitionId { get; set; }
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; } // Nullable until partner joins

    [MaxLength(50)]
    public string TeamName { get; set; } = string.Empty;

    [MaxLength(10)]
    public string InviteCode { get; set; } = string.Empty; // Code for partner invitation

    public int GamesPlayed { get; set; } = 0;
    public int Points { get; set; } = 0; // Total points earned
    public int BonusPoints { get; set; } = 0; // Bonus points for 6+ ball difference wins
    public int MaxGames { get; set; } = 10; // Maximum games allowed (configurable)

    public bool IsActive { get; set; } = true;
    public bool IsComplete { get; set; } = false; // True when partner has joined

    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; } // When partner joined

    // Phase Management
    [MaxLength(40)]
    public string Phase { get; set; } = "Leaderboard";
    public bool IsEliminated { get; set; } = false;
    public bool AdvancedToNextPhase { get; set; } = false;
    public DateTime? PhaseEliminatedAt { get; set; }

    // Navigation properties
    public virtual Competition Competition { get; set; } = null!;
    public virtual User Player1 { get; set; } = null!;
    public virtual User? Player2 { get; set; }
}
