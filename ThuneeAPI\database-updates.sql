-- Database updates to support Competition Phase Management
-- Run these SQL commands in SSMS to add the missing columns

-- Add Phase Management columns to Competitions table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'Phase')
BEGIN
    ALTER TABLE Competitions ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'PhaseEndDate')
BEGIN
    ALTER TABLE Competitions ADD PhaseEndDate DATETIME2 NULL;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'MaxGamesPerPhase')
BEGIN
    ALTER TABLE Competitions ADD MaxGamesPerPhase BIGINT NULL;
END

-- Add Phase Management columns to CompetitionTeams table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'Phase')
BEGIN
    ALTER TABLE CompetitionTeams ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'IsEliminated')
BEGIN
    ALTER TABLE CompetitionTeams ADD IsEliminated BIT NOT NULL DEFAULT 0;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'AdvancedToNextPhase')
BEGIN
    ALTER TABLE CompetitionTeams ADD AdvancedToNextPhase BIT NOT NULL DEFAULT 0;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'PhaseEliminatedAt')
BEGIN
    ALTER TABLE CompetitionTeams ADD PhaseEliminatedAt DATETIME2 NULL;
END

-- Update existing competitions to have default phase
UPDATE Competitions SET Phase = 'Leaderboard' WHERE Phase IS NULL OR Phase = '';

-- Update existing competition teams to have default phase
UPDATE CompetitionTeams SET Phase = 'Leaderboard' WHERE Phase IS NULL OR Phase = '';

PRINT 'Database schema updated successfully for Competition Phase Management';
