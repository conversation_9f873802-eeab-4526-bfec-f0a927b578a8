
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using System.Security.Claims;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/competitions/{competitionId}/phases")]
[Authorize]
public class CompetitionPhaseController : ControllerBase
{
    private readonly ICompetitionPhaseService _phaseService;
    private readonly ILogger<CompetitionPhaseController> _logger;

    public CompetitionPhaseController(
        ICompetitionPhaseService phaseService,
        ILogger<CompetitionPhaseController> logger)
    {
        _phaseService = phaseService;
        _logger = logger;
    }

    /// <summary>
    /// Advance competition to next phase (Admin only)
    /// </summary>
    [HttpPost("advance")]
    public async Task<ActionResult<CompetitionDto>> AdvancePhase(Guid competitionId, [FromBody] AdvancePhaseDto dto)
    {
        try
        {
            var competition = await _phaseService.AdvanceCompetitionPhaseAsync(competitionId, dto.NewPhase);
            
            _logger.LogInformation("Competition {CompetitionId} advanced to phase {Phase}", competitionId, dto.NewPhase);
            
            return Ok(new
            {
                success = true,
                data = competition,
                message = $"Competition advanced to {dto.NewPhase} phase"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}", competitionId, dto.NewPhase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Get teams for specific phase
    /// </summary>
    [HttpGet("{phase}/teams")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetPhaseTeams(Guid competitionId, string phase)
    {
        try
        {
            var teams = await _phaseService.GetTeamsForPhaseAsync(competitionId, phase);
            
            return Ok(new
            {
                success = true,
                data = teams
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Get eligible teams for next phase
    /// </summary>
    [HttpGet("eligible")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetEligibleTeams(Guid competitionId)
    {
        try
        {
            var teams = await _phaseService.GetEligibleTeamsForNextPhaseAsync(competitionId);
            
            return Ok(new
            {
                success = true,
                data = teams
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting eligible teams for competition {CompetitionId}", competitionId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Process phase end and advance teams (Admin only)
    /// </summary>
    [HttpPost("process-end")]
    public async Task<ActionResult> ProcessPhaseEnd(Guid competitionId)
    {
        try
        {
            await _phaseService.ProcessPhaseEndAsync(competitionId);
            
            _logger.LogInformation("Phase end processed for competition {CompetitionId}", competitionId);
            
            return Ok(new
            {
                success = true,
                message = "Phase end processed successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing phase end for competition {CompetitionId}", competitionId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Create phase lobby for knockout rounds (Admin only)
    /// </summary>
    [HttpPost("{phase}/lobbies")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> CreatePhaseLobby(
        Guid competitionId, 
        string phase, 
        [FromBody] CreatePhaseLobbyDto dto)
    {
        try
        {
            var adminId = GetCurrentUserId();
            var createDto = new CreateCompetitionPhaseLobbyDto
            {
                CompetitionId = competitionId,
                Phase = phase,
                TeamIds = dto.TeamIds
            };
            
            var lobby = await _phaseService.CreatePhaseLobbyAsync(createDto, adminId);
            
            _logger.LogInformation("Phase lobby created for competition {CompetitionId} phase {Phase}", competitionId, phase);
            
            return Ok(new
            {
                success = true,
                data = lobby,
                message = "Phase lobby created successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating phase lobby for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Get phase lobbies
    /// </summary>
    [HttpGet("{phase}/lobbies")]
    public async Task<ActionResult<List<CompetitionPhaseLobbyDto>>> GetPhaseLobbies(Guid competitionId, string phase)
    {
        try
        {
            var lobbies = await _phaseService.GetPhaseLobbiesAsync(competitionId, phase);
            
            return Ok(new
            {
                success = true,
                data = lobbies
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobbies for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Generate bracket for phase
    /// </summary>
    [HttpGet("{phase}/bracket")]
    public async Task<ActionResult<BracketDto>> GetPhaseBracket(Guid competitionId, string phase)
    {
        try
        {
            var bracket = await _phaseService.GenerateBracketAsync(competitionId, phase);
            
            return Ok(new
            {
                success = true,
                data = bracket
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating bracket for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Get all competition brackets
    /// </summary>
    [HttpGet("brackets")]
    public async Task<ActionResult<List<BracketDto>>> GetCompetitionBrackets(Guid competitionId)
    {
        try
        {
            var brackets = await _phaseService.GetCompetitionBracketsAsync(competitionId);
            
            return Ok(new
            {
                success = true,
                data = brackets
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting brackets for competition {CompetitionId}", competitionId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Set lobby winner (Admin only)
    /// </summary>
    [HttpPost("lobbies/{lobbyId}/winner")]
    public async Task<ActionResult> SetLobbyWinner(Guid competitionId, Guid lobbyId, [FromBody] SetLobbyWinnerDto dto)
    {
        try
        {
            await _phaseService.SetLobbyWinnerAsync(lobbyId, dto.WinnerTeamId);
            
            _logger.LogInformation("Winner set for lobby {LobbyId} in competition {CompetitionId}", lobbyId, competitionId);
            
            return Ok(new
            {
                success = true,
                message = "Lobby winner set successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting winner for lobby {LobbyId}", lobbyId);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    /// <summary>
    /// Get phase rankings
    /// </summary>
    [HttpGet("{phase}/rankings")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetPhaseRankings(Guid competitionId, string phase)
    {
        try
        {
            var rankings = await _phaseService.CalculatePhaseRankingsAsync(competitionId, phase);
            
            return Ok(new
            {
                success = true,
                data = rankings
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase rankings for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return BadRequest(new { success = false, message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid user ID in token");
        }
        return userId;
    }
}

// Supporting DTOs
public class AdvancePhaseDto
{
    public string NewPhase { get; set; } = string.Empty;
}

public class CreatePhaseLobbyDto
{
    public List<Guid> TeamIds { get; set; } = new();
}

public class SetLobbyWinnerDto
{
    public Guid WinnerTeamId { get; set; }
}
