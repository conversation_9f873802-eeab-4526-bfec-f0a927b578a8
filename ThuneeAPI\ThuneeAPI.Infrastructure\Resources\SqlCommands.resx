<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="xml:space" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Competition Phase Management Stored Procedures -->
  <data name="SP_AdvanceCompetitionPhase" xml:space="preserve">
    <value>SP_AdvanceCompetitionPhase</value>
  </data>
  <data name="SP_GetEligibleTeamsForPhase" xml:space="preserve">
    <value>SP_GetEligibleTeamsForPhase</value>
  </data>
  <data name="SP_AdvanceTeamsToPhase" xml:space="preserve">
    <value>SP_AdvanceTeamsToPhase</value>
  </data>
  <data name="SP_EliminateTeams" xml:space="preserve">
    <value>SP_EliminateTeams</value>
  </data>
  <data name="SP_GetTeamsByPhase" xml:space="preserve">
    <value>SP_GetTeamsByPhase</value>
  </data>
  <data name="SP_CreateCompetitionPhaseLobby" xml:space="preserve">
    <value>SP_CreateCompetitionPhaseLobby</value>
  </data>
  <data name="SP_GetCompetitionPhaseLobbies" xml:space="preserve">
    <value>SP_GetCompetitionPhaseLobbies</value>
  </data>
  <data name="SP_SetPhaseLobbyWinner" xml:space="preserve">
    <value>SP_SetPhaseLobbyWinner</value>
  </data>
  <data name="SP_GetCompetitionPhaseStats" xml:space="preserve">
    <value>SP_GetCompetitionPhaseStats</value>
  </data>
  <data name="SP_UpdateCompetitionTeamPhase" xml:space="preserve">
    <value>SP_UpdateCompetitionTeamPhase</value>
  </data>
  
  <!-- Competition Team Management -->
  <data name="SP_GetCompetitionTeamsByPhase" xml:space="preserve">
    <value>SP_GetCompetitionTeamsByPhase</value>
  </data>
  <data name="SP_CheckTeamEligibilityForGames" xml:space="preserve">
    <value>SP_CheckTeamEligibilityForGames</value>
  </data>
  
</root>
