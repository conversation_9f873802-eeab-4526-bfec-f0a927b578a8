using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using Microsoft.Data.SqlClient;
using Dapper;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/competitions/{competitionId}/phases")]
[Authorize]
public class CompetitionPhaseSimpleController : ControllerBase
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<CompetitionPhaseSimpleController> _logger;

    public CompetitionPhaseSimpleController(
        IConfiguration configuration,
        ILogger<CompetitionPhaseSimpleController> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    [HttpPost("advance")]
    public async Task<IActionResult> AdvancePhase(Guid competitionId, [FromBody] AdvancePhaseRequest request)
    {
        try
        {
            _logger.LogInformation("Advancing competition {CompetitionId} to phase {NewPhase}", competitionId, request.NewPhase);

            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            
            // First, get the current competition details
            var competition = await connection.QueryFirstOrDefaultAsync<Competition>(
                "SELECT * FROM Competitions WHERE Id = @CompetitionId",
                new { CompetitionId = competitionId });

            if (competition == null)
            {
                return NotFound(new { message = "Competition not found" });
            }

            // Get top teams based on current phase
            var advancingCount = request.NewPhase switch
            {
                "Top32" => 32,
                "Top16" => 16,
                "Top8" => 8,
                "Top4" => 4,
                "Final" => 2,
                _ => 0
            };

            if (advancingCount == 0)
            {
                return BadRequest(new { message = "Invalid phase transition" });
            }

            // Get eligible teams (top teams by total points)
            var eligibleTeams = await connection.QueryAsync<CompetitionTeam>(
                @"SELECT TOP (@AdvancingCount) * 
                  FROM CompetitionTeams 
                  WHERE CompetitionId = @CompetitionId 
                  AND IsEliminated = 0 
                  AND Phase = @CurrentPhase
                  ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC",
                new { 
                    AdvancingCount = advancingCount, 
                    CompetitionId = competitionId,
                    CurrentPhase = competition.Phase 
                });

            // Get teams to eliminate
            var allActiveTeams = await connection.QueryAsync<CompetitionTeam>(
                @"SELECT * FROM CompetitionTeams 
                  WHERE CompetitionId = @CompetitionId 
                  AND IsEliminated = 0 
                  AND Phase = @CurrentPhase",
                new { CompetitionId = competitionId, CurrentPhase = competition.Phase });

            var teamsToEliminate = allActiveTeams.Where(t => !eligibleTeams.Any(e => e.Id == t.Id)).ToList();

            // Start transaction
            using var transaction = connection.BeginTransaction();

            try
            {
                // Advance eligible teams
                foreach (var team in eligibleTeams)
                {
                    await connection.ExecuteAsync(
                        @"UPDATE CompetitionTeams 
                          SET Phase = @NewPhase, AdvancedToNextPhase = 1, UpdatedAt = GETUTCDATE()
                          WHERE Id = @TeamId",
                        new { NewPhase = request.NewPhase, TeamId = team.Id },
                        transaction);
                }

                // Eliminate remaining teams
                foreach (var team in teamsToEliminate)
                {
                    await connection.ExecuteAsync(
                        @"UPDATE CompetitionTeams 
                          SET IsEliminated = 1, PhaseEliminatedAt = GETUTCDATE()
                          WHERE Id = @TeamId",
                        new { TeamId = team.Id },
                        transaction);
                }

                // Update competition phase
                var phaseEndDate = request.NewPhase switch
                {
                    "Top32" => DateTime.UtcNow.AddDays(7),
                    "Top16" => DateTime.UtcNow.AddDays(3),
                    "Top8" => DateTime.UtcNow.AddDays(2),
                    "Top4" => DateTime.UtcNow.AddDays(1),
                    "Final" => DateTime.UtcNow.AddHours(12),
                    _ => (DateTime?)null
                };

                await connection.ExecuteAsync(
                    @"UPDATE Competitions 
                      SET Phase = @NewPhase, PhaseEndDate = @PhaseEndDate, UpdatedAt = GETUTCDATE()
                      WHERE Id = @CompetitionId",
                    new { 
                        NewPhase = request.NewPhase, 
                        PhaseEndDate = phaseEndDate,
                        CompetitionId = competitionId 
                    },
                    transaction);

                transaction.Commit();

                _logger.LogInformation("Successfully advanced {AdvancedCount} teams to {NewPhase} and eliminated {EliminatedCount} teams",
                    eligibleTeams.Count(), request.NewPhase, teamsToEliminate.Count);

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        id = competitionId,
                        phase = request.NewPhase,
                        phaseEndDate = phaseEndDate,
                        advancedTeams = eligibleTeams.Count(),
                        eliminatedTeams = teamsToEliminate.Count
                    },
                    message = $"Competition advanced to {request.NewPhase} phase"
                });
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition phase");
            return StatusCode(500, new { message = "Failed to advance phase", error = ex.Message });
        }
    }

    [HttpPost("process-end")]
    public async Task<IActionResult> ProcessPhaseEnd(Guid competitionId)
    {
        try
        {
            _logger.LogInformation("Processing phase end for competition {CompetitionId}", competitionId);

            using var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection"));
            
            // Get current competition
            var competition = await connection.QueryFirstOrDefaultAsync<Competition>(
                "SELECT * FROM Competitions WHERE Id = @CompetitionId",
                new { CompetitionId = competitionId });

            if (competition == null)
            {
                return NotFound(new { message = "Competition not found" });
            }

            // Determine next phase
            var nextPhase = competition.Phase switch
            {
                "Leaderboard" => "Top32",
                "Top32" => "Top16",
                "Top16" => "Top8",
                "Top8" => "Top4",
                "Top4" => "Final",
                "Final" => "Completed",
                _ => null
            };

            if (nextPhase == null)
            {
                return BadRequest(new { message = "Cannot advance from current phase" });
            }

            // Use the advance endpoint logic
            var request = new AdvancePhaseRequest { NewPhase = nextPhase };
            return await AdvancePhase(competitionId, request);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing phase end");
            return StatusCode(500, new { message = "Failed to process phase end", error = ex.Message });
        }
    }
}

public class AdvancePhaseRequest
{
    public string NewPhase { get; set; } = string.Empty;
}

public class Competition
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Phase { get; set; } = string.Empty;
    public DateTime? PhaseEndDate { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CompetitionTeam
{
    public Guid Id { get; set; }
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; }
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public string Phase { get; set; } = string.Empty;
    public bool IsEliminated { get; set; }
    public bool AdvancedToNextPhase { get; set; }
    public DateTime? PhaseEliminatedAt { get; set; }
}
